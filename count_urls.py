import pandas as pd
import re
from urllib.parse import urlparse

# Read the CSV file
df = pd.read_csv('Tamarind600.csv')

# Get factoid source columns (pattern: number. factoid_X_answer_source)
factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]

# Get insight source columns (pattern: number. insight_X_source)
insight_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+insight_\d+_source$', col)]

print(f"Found {len(factoid_source_columns)} factoid source columns")
print(f"Found {len(insight_source_columns)} insight source columns")

def is_valid_url(url):
    """Check if the string is a valid URL"""
    try:
        result = urlparse(str(url))
        return all([result.scheme, result.netloc])
    except:
        return False

# Count URLs
total_factoid_urls = 0
total_insight_urls = 0
valid_factoid_urls = 0
valid_insight_urls = 0

print("Counting URLs...")

for idx, row in df.iterrows():
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    # Count factoid URLs
    for col in factoid_source_columns:
        if not pd.isna(row[col]) and str(row[col]).strip() != '':
            total_factoid_urls += 1
            url = str(row[col]).strip()
            if is_valid_url(url):
                valid_factoid_urls += 1
    
    # Count insight URLs
    for col in insight_source_columns:
        if not pd.isna(row[col]) and str(row[col]).strip() != '':
            total_insight_urls += 1
            url = str(row[col]).strip()
            if is_valid_url(url):
                valid_insight_urls += 1

print(f"\n=== URL COUNT SUMMARY ===")
print(f"Total factoid URLs: {total_factoid_urls}")
print(f"Valid factoid URLs: {valid_factoid_urls}")
print(f"Total insight URLs: {total_insight_urls}")
print(f"Valid insight URLs: {valid_insight_urls}")
print(f"Total valid URLs to check: {valid_factoid_urls + valid_insight_urls}")

# Show some sample URLs
print(f"\n=== SAMPLE URLS ===")
sample_count = 0
for idx, row in df.iterrows():
    if sample_count >= 5:
        break
        
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    for col in factoid_source_columns + insight_source_columns:
        if sample_count >= 5:
            break
        if not pd.isna(row[col]) and str(row[col]).strip() != '':
            url = str(row[col]).strip()
            if is_valid_url(url):
                print(f"Sample URL {sample_count + 1}: {url}")
                sample_count += 1
