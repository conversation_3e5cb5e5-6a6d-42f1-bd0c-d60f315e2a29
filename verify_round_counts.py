import pandas as pd

# Read the updated CSV file
df = pd.read_csv('factoid_completeness_data_with_round_counts.csv')

print("=== ROUND COUNT STATISTICS ===")
print(f"Total rows: {len(df)}")
print(f"Rows with worker_id: {len(df[df['worker_id'].notna() & (df['worker_id'] != '')])}")

# Filter to only rows with worker_id for analysis
df_with_worker = df[df['worker_id'].notna() & (df['worker_id'] != '')]
print(f"Analysis based on {len(df_with_worker)} rows with worker_id")

print("\n=== FACTOID ROUNDS ===")
print(f"Mean rounds with factoids: {df_with_worker['rounds_with_factoids'].mean():.2f}")
print(f"Min rounds with factoids: {df_with_worker['rounds_with_factoids'].min()}")
print(f"Max rounds with factoids: {df_with_worker['rounds_with_factoids'].max()}")
print("Distribution:")
print(df_with_worker['rounds_with_factoids'].value_counts().sort_index())

print("\n=== INSIGHT ROUNDS ===")
print(f"Mean rounds with insights: {df_with_worker['rounds_with_insights'].mean():.2f}")
print(f"Min rounds with insights: {df_with_worker['rounds_with_insights'].min()}")
print(f"Max rounds with insights: {df_with_worker['rounds_with_insights'].max()}")
print("Distribution:")
print(df_with_worker['rounds_with_insights'].value_counts().sort_index())

print("\n=== INSTRUCTION FOLLOWING ROUNDS ===")
print(f"Mean rounds with instruction following: {df_with_worker['rounds_with_instruction_following'].mean():.2f}")
print(f"Min rounds with instruction following: {df_with_worker['rounds_with_instruction_following'].min()}")
print(f"Max rounds with instruction following: {df_with_worker['rounds_with_instruction_following'].max()}")
print("Distribution:")
print(df_with_worker['rounds_with_instruction_following'].value_counts().sort_index())

print("\n=== QUALITY CHECKS ===")
print(f"Rows with < 3 factoid rounds: {len(df_with_worker[df_with_worker['rounds_with_factoids'] < 3])}")
print(f"Rows with < 3 insight rounds: {len(df_with_worker[df_with_worker['rounds_with_insights'] < 3])}")
print(f"Rows with < 2 instruction following rounds: {len(df_with_worker[df_with_worker['rounds_with_instruction_following'] < 2])}")

print("\n=== SAMPLE DATA ===")
print("First 10 rows with round counts:")
sample_cols = ['task_id', 'worker_id', 'rounds_with_factoids', 'rounds_with_insights', 'rounds_with_instruction_following']
available_cols = [col for col in sample_cols if col in df.columns]
print(df_with_worker[available_cols].head(10).to_string(index=False))
