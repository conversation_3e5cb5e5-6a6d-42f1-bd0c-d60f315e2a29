import pandas as pd
import re
import requests
import time
from urllib.parse import urlparse
import csv

# Read the CSV file
df = pd.read_csv('Tamarind600.csv')

# Get factoid source columns (pattern: number. factoid_X_answer_source)
factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]

# Get insight source columns (pattern: number. insight_X_source)
insight_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+insight_\d+_source$', col)]

print(f"Found {len(factoid_source_columns)} factoid source columns")
print(f"Found {len(insight_source_columns)} insight source columns")

# Initialize results list
url_404_results = []

def is_valid_url(url):
    """Check if the string is a valid URL"""
    try:
        result = urlparse(str(url))
        return all([result.scheme, result.netloc])
    except:
        return False

def check_url_404(url, timeout=10):
    """Check if URL returns 404 error"""
    try:
        response = requests.head(url, timeout=timeout, allow_redirects=True)
        return response.status_code == 404
    except requests.exceptions.RequestException:
        # If there's any connection error, we'll consider it as not a 404
        # (could be timeout, connection refused, etc.)
        return False

# Process each row
total_rows = len(df)
processed_rows = 0
total_urls_checked = 0

print(f"Starting to process {total_rows} rows...")

for idx, row in df.iterrows():
    processed_rows += 1
    if processed_rows % 10 == 0:
        print(f"Processed {processed_rows}/{total_rows} rows, checked {total_urls_checked} URLs so far...")
    
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    task_id = row['task_id'] if 'task_id' in df.columns else 'Unknown'
    
    # Check factoid source columns
    for col in factoid_source_columns:
        # Skip if the column is empty
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        url = str(row[col]).strip()
        
        # Check if it's a valid URL
        if not is_valid_url(url):
            continue

        total_urls_checked += 1

        # Extract round number and factoid number
        match = re.match(r'(\d+)\.\s+factoid_(\d+)_answer_source$', col)
        if match:
            round_num = match.group(1)
            factoid_num = match.group(2)

            # Check for 404
            if check_url_404(url):
                url_404_results.append({
                    'task_id': task_id,
                    'round': round_num,
                    'type': 'factoid',
                    'instance_within_round': factoid_num,
                    'column': col,
                    'url': url
                })
                print(f"404 found: Row {idx}, {col}, URL: {url}")

        # Add small delay to be respectful to servers
        time.sleep(0.1)
    
    # Check insight source columns
    for col in insight_source_columns:
        # Skip if the column is empty
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        url = str(row[col]).strip()
        
        # Check if it's a valid URL
        if not is_valid_url(url):
            continue

        total_urls_checked += 1

        # Extract round number and insight number
        match = re.match(r'(\d+)\.\s+insight_(\d+)_source$', col)
        if match:
            round_num = match.group(1)
            insight_num = match.group(2)

            # Check for 404
            if check_url_404(url):
                url_404_results.append({
                    'task_id': task_id,
                    'round': round_num,
                    'type': 'insight',
                    'instance_within_round': insight_num,
                    'column': col,
                    'url': url
                })
                print(f"404 found: Row {idx}, {col}, URL: {url}")

        # Add small delay to be respectful to servers
        time.sleep(0.1)

print(f"\nProcessing complete. Checked {total_urls_checked} total URLs. Found {len(url_404_results)} URLs returning 404 errors.")

# Save results to CSV
if url_404_results:
    output_filename = 'urls_returning_404.csv'
    
    with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['task_id', 'round', 'type', 'instance_within_round', 'column', 'url']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in url_404_results:
            writer.writerow(result)
    
    print(f"Results saved to: {output_filename}")
    
    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total 404 URLs found: {len(url_404_results)}")
    
    # Count by type
    factoid_404s = len([r for r in url_404_results if r['type'] == 'factoid'])
    insight_404s = len([r for r in url_404_results if r['type'] == 'insight'])
    
    print(f"Factoid 404s: {factoid_404s}")
    print(f"Insight 404s: {insight_404s}")
    
    # Count by round
    rounds = {}
    for result in url_404_results:
        round_num = result['round']
        if round_num not in rounds:
            rounds[round_num] = 0
        rounds[round_num] += 1
    
    print(f"\n404s by round:")
    for round_num in sorted(rounds.keys()):
        print(f"  Round {round_num}: {rounds[round_num]} URLs")
    
    # Show first few results
    print(f"\nFirst 5 404 URLs found:")
    for i, result in enumerate(url_404_results[:5]):
        print(f"  {i+1}. Task {result['task_id']}, Round {result['round']}, {result['type']} {result['instance_within_round']}: {result['url']}")

else:
    print("No URLs returning 404 errors were found.")
    # Create empty CSV file
    with open('urls_returning_404.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['task_id', 'round', 'type', 'instance_within_round', 'column', 'url']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
    print("Empty results file created: urls_returning_404.csv")
