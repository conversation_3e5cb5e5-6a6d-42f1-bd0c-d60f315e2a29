import pandas as pd
import re
import requests

print("Starting URL check test...")

# Test if we can import everything
try:
    df = pd.read_csv('Tamarind600.csv')
    print(f"Successfully loaded CSV with {len(df)} rows")
except Exception as e:
    print(f"Error loading CSV: {e}")
    exit(1)

# Get factoid source columns
factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]
insight_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+insight_\d+_source$', col)]

print(f"Found {len(factoid_source_columns)} factoid source columns")
print(f"Found {len(insight_source_columns)} insight source columns")

# Test a simple URL check
test_url = "https://httpbin.org/status/404"
try:
    response = requests.head(test_url, timeout=5)
    print(f"Test URL check successful. Status: {response.status_code}")
except Exception as e:
    print(f"Error with test URL: {e}")

print("Test completed successfully!")
