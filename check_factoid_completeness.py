import pandas as pd
import re
from collections import defaultdict

# Load the CSV file
file_path = "tesbad3.csv"
df = pd.read_csv(file_path)

# Function to check if a string contains a URL
# Function to check if a string contains a URL
def contains_url(text):
    if pd.isna(text):
        return False
    url_pattern = re.compile(r'https?://\S+|www\.\S+')
    return bool(url_pattern.search(str(text)))

# Get all column names that match the patterns
factoid_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+$', col)]
insight_columns = [col for col in df.columns if re.match(r'\d+\.\s+insight_\d+$', col)]
instruction_columns = [col for col in df.columns if re.match(r'\d+\.\s+instruction_following_\d+$', col)]

# Initialize results by category
missing_source_issues = []
missing_url_issues = []
missing_type_issues = []
total_instance_issues = []  # New separate category for total instance count
round_count_issues = []     # New separate category for round count
instance_per_round_issues = []  # New separate category for instances per round
duplicate_instruction_issues = []  # New category for duplicate instructions
duplicate_factoid_issues = []  # New category for duplicate factoids
duplicate_insight_issues = []  # New category for duplicate insights
below_20_after_dedup_issues = []  # New category for below 20 after deduplication

# Process each row
for idx, row in df.iterrows():
    # Get IDs
    task_id = row['task_id'] if 'task_id' in df.columns else 'Unknown'
    task_response_id = row['task_response_id'] if 'task_response_id' in df.columns else 'Unknown'
    
    # Track issues for this row
    source_issues = []
    url_issues = []
    type_issues = []
    
    # Count instances and rounds
    factoid_instances = 0
    insight_instances = 0
    instruction_instances = 0
    factoid_insight_rounds = set()
    instruction_rounds = set()
    instances_by_round = defaultdict(lambda: {'factoid': 0, 'insight': 0, 'instruction': 0})
    
    # Track instruction following text for duplicate detection
    instruction_texts = []
    factoid_texts = []    # New list for factoid texts
    insight_texts = []    # New list for insight texts
    
    # Check factoid columns
    for factoid_col in factoid_columns:
        # Skip if the factoid column is empty
        if pd.isna(row[factoid_col]) or str(row[factoid_col]).strip() == '':
            continue
            
        # Get the text for duplicate checking
        factoid_text = str(row[factoid_col]).strip()
        factoid_texts.append(factoid_text)
            
        # Count this instance
        factoid_instances += 1
        
        # Extract round number
        round_match = re.match(r'(\d+)\.', factoid_col)
        if round_match:
            round_num = round_match.group(1)
            factoid_insight_rounds.add(round_num)
            instances_by_round[round_num]['factoid'] += 1
            
        # Extract the prefix (e.g., "1. factoid_1" -> "1. factoid_")
        prefix_match = re.match(r'(\d+\.\s+factoid_)\d+', factoid_col)
        if not prefix_match:
            continue
            
        prefix = prefix_match.group(1)
        suffix = factoid_col.split(prefix)[1]
        
        # Construct the source column name
        source_col = f"{prefix}{suffix}_answer_source"
        
        # Check if this column exists
        if source_col not in df.columns:
            source_issues.append(f"Missing column for {factoid_col}")
            continue
            
        # Check if source column has text and contains a URL
        if pd.isna(row[source_col]) or str(row[source_col]).strip() == '':
            source_issues.append(f"Missing source for {factoid_col}")
        elif not contains_url(row[source_col]):
            url_issues.append(f"Source for {factoid_col} doesn't contain a URL. Content: '{row[source_col]}'")
    
    # Check insight columns
    for insight_col in insight_columns:
        # Skip if the insight column is empty
        if pd.isna(row[insight_col]) or str(row[insight_col]).strip() == '':
            continue
            
        # Count this instance
        insight_instances += 1
        
        # Extract round number
        round_match = re.match(r'(\d+)\.', insight_col)
        if round_match:
            round_num = round_match.group(1)
            factoid_insight_rounds.add(round_num)
            instances_by_round[round_num]['insight'] += 1
    
    # Check instruction_following columns
    for instruction_col in instruction_columns:
        # Skip if the instruction column is empty
        if pd.isna(row[instruction_col]) or str(row[instruction_col]).strip() == '':
            continue
            
        # Get the text for duplicate checking
        instruction_text = str(row[instruction_col]).strip()
        instruction_texts.append(instruction_text)
        
        # Count this instance
        instruction_instances += 1
        
        # Extract round number
        round_match = re.match(r'(\d+)\.', instruction_col)
        if round_match:
            round_num = round_match.group(1)
            instruction_rounds.add(round_num)
            instances_by_round[round_num]['instruction'] += 1
            
        # Extract the prefix (e.g., "1. instruction_following_1" -> "1. instruction_following_")
        prefix_match = re.match(r'(\d+\.\s+instruction_following_)\d+', instruction_col)
        if not prefix_match:
            continue
            
        prefix = prefix_match.group(1)
        suffix = instruction_col.split(prefix)[1]
        
        # Construct the type column name
        type_col = f"{prefix}{suffix}_type"
        
        # Check if this column exists
        if type_col not in df.columns:
            type_issues.append(f"Missing type column for {instruction_col}")
            continue
            
        # Check if type column has text
        if pd.isna(row[type_col]) or str(row[type_col]).strip() == '':
            type_issues.append(f"Missing type for {instruction_col}. Content: '{row[type_col]}'")
    
    # Add issues to the appropriate categories
    if source_issues:
        missing_source_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': source_issues
        })
    
    if url_issues:
        missing_url_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': url_issues
        })
    
    if type_issues:
        missing_type_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': type_issues
        })
    
    # Check for duplicate instruction following instances
    duplicate_instructions = []
    seen_texts = {}  # Changed to dict to track counts
    duplicate_count = 0
    
    for text in instruction_texts:
        if text in seen_texts:
            seen_texts[text] += 1
            duplicate_count += 1
            # Only add to issues list if this is the first duplicate of this text
            if seen_texts[text] == 2:
                duplicate_instructions.append(f"Instruction text '{text[:50]}...' appears {seen_texts[text]} times")
            else:
                # Update the count in the existing issue
                for i, issue in enumerate(duplicate_instructions):
                    if issue.startswith(f"Instruction text '{text[:50]}"):
                        duplicate_instructions[i] = f"Instruction text '{text[:50]}...' appears {seen_texts[text]} times"
                        break
        else:
            seen_texts[text] = 1
    
    if duplicate_instructions:
        duplicate_instruction_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': duplicate_instructions
        })
    
    # Check for duplicate factoid instances
    duplicate_factoids = []
    seen_factoid_texts = {}
    factoid_duplicate_count = 0

    for text in factoid_texts:
        if text in seen_factoid_texts:
            seen_factoid_texts[text] += 1
            factoid_duplicate_count += 1
            # Only add to issues list if this is the first duplicate of this text
            if seen_factoid_texts[text] == 2:
                duplicate_factoids.append(f"Factoid text '{text[:50]}...' appears {seen_factoid_texts[text]} times")
            else:
                # Update the count in the existing issue
                for i, issue in enumerate(duplicate_factoids):
                    if issue.startswith(f"Factoid text '{text[:50]}"):
                        duplicate_factoids[i] = f"Factoid text '{text[:50]}...' appears {seen_factoid_texts[text]} times"
                        break
        else:
            seen_factoid_texts[text] = 1

    if duplicate_factoids:
        duplicate_factoid_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': duplicate_factoids
        })

    # Check for duplicate insight instances
    duplicate_insights = []
    seen_insight_texts = {}
    insight_duplicate_count = 0

    for text in insight_texts:
        if text in seen_insight_texts:
            seen_insight_texts[text] += 1
            insight_duplicate_count += 1
            # Only add to issues list if this is the first duplicate of this text
            if seen_insight_texts[text] == 2:
                duplicate_insights.append(f"Insight text '{text[:50]}...' appears {seen_insight_texts[text]} times")
            else:
                # Update the count in the existing issue
                for i, issue in enumerate(duplicate_insights):
                    if issue.startswith(f"Insight text '{text[:50]}"):
                        duplicate_insights[i] = f"Insight text '{text[:50]}...' appears {seen_insight_texts[text]} times"
                        break
        else:
            seen_insight_texts[text] = 1

    if duplicate_insights:
        duplicate_insight_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': duplicate_insights
        })
    
    # Check if removing all duplicates would bring total below 20
    unique_factoid_count = len(seen_factoid_texts)
    unique_insight_count = len(seen_insight_texts)
    unique_instruction_count = len(seen_texts)
    
    total_instances = factoid_instances + insight_instances + instruction_instances
    total_after_all_dedup = (factoid_instances - factoid_duplicate_count) + \
                           (insight_instances - insight_duplicate_count) + \
                           (instruction_instances - duplicate_count)
    
    total_duplicates = factoid_duplicate_count + insight_duplicate_count + duplicate_count
    
    if total_after_all_dedup < 20 and total_duplicates > 0:
        below_20_after_dedup_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': [
                f"Starting total: {total_instances} instances",
                f"Duplicates: {factoid_duplicate_count} factoids, {insight_duplicate_count} insights, {duplicate_count} instructions",
                f"After deduplicating all types: {total_after_all_dedup} instances (below 20)"
            ]
        })
    
    # Check total instances (separate category)
    total_instances = factoid_instances + insight_instances + instruction_instances
    if total_instances < 20:
        total_instance_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': [f"Only {total_instances} total instances (less than 20)"]
        })
    
    # Check distinct rounds (separate category)
    round_issues = []
    
    if len(factoid_insight_rounds) < 3:
        round_issues.append(f"Only {len(factoid_insight_rounds)} distinct rounds with factoids/insights (less than 3)")
    
    if len(instruction_rounds) < 2:
        round_issues.append(f"Only {len(instruction_rounds)} distinct rounds with instruction following (less than 2)")
    
    if round_issues:
        round_count_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': round_issues
        })
    
    # Check instances per round (separate category)
    rounds_with_few_instances = []
    for round_num, counts in instances_by_round.items():
        total_in_round = sum(counts.values())
        if total_in_round < 3:
            rounds_with_few_instances.append(f"Round {round_num} has only {total_in_round} instances")
    
    if rounds_with_few_instances:
        instance_per_round_issues.append({
            'row_index': idx,
            'task_id': task_id,
            'task_response_id': task_response_id,
            'issues': ["Rounds with fewer than 3 instances: " + ", ".join(rounds_with_few_instances)]
        })

# Print results by category
print("=== MISSING SOURCE ISSUES ===")
if missing_source_issues:
    print(f"Found {len(missing_source_issues)} rows with missing sources:")
    for result in missing_source_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No missing source issues found.")

print("\n=== MISSING URL ISSUES ===")
if missing_url_issues:
    print(f"Found {len(missing_url_issues)} rows with missing URLs:")
    for result in missing_url_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No missing URL issues found.")

print("\n=== MISSING TYPE ISSUES ===")
if missing_type_issues:
    print(f"Found {len(missing_type_issues)} rows with missing types:")
    for result in missing_type_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No missing type issues found.")

print("\n=== TOTAL INSTANCE COUNT ISSUES ===")
if total_instance_issues:
    print(f"Found {len(total_instance_issues)} rows with too few total instances:")
    for result in total_instance_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No total instance count issues found.")

print("\n=== ROUND COUNT ISSUES ===")
if round_count_issues:
    print(f"Found {len(round_count_issues)} rows with too few rounds:")
    for result in round_count_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No round count issues found.")

print("\n=== INSTANCES PER ROUND ISSUES ===")
if instance_per_round_issues:
    print(f"Found {len(instance_per_round_issues)} rows with too few instances per round:")
    for result in instance_per_round_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No instances per round issues found.")

print("\n=== DUPLICATE INSTRUCTION INSTANCES ISSUES ===")
if duplicate_instruction_issues:
    print(f"Found {len(duplicate_instruction_issues)} rows with duplicate instruction instances:")
    for result in duplicate_instruction_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No duplicate instruction instances found.")

print("\n=== DUPLICATE FACTOID INSTANCES ISSUES ===")
if duplicate_factoid_issues:
    print(f"Found {len(duplicate_factoid_issues)} rows with duplicate factoid instances:")
    for result in duplicate_factoid_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No duplicate factoid instances found.")

print("\n=== DUPLICATE INSIGHT INSTANCES ISSUES ===")
if duplicate_insight_issues:
    print(f"Found {len(duplicate_insight_issues)} rows with duplicate insight instances:")
    for result in duplicate_insight_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No duplicate insight instances found.")

print("\n=== BELOW 20 AFTER DEDUPLICATION ISSUES ===")
if below_20_after_dedup_issues:
    print(f"Found {len(below_20_after_dedup_issues)} rows that would fall below 20 instances after removing duplicates:")
    for result in below_20_after_dedup_issues:
        print(f"Row {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}):")
        for issue in result['issues']:
            print(f"  - {issue}")
else:
    print("No rows would fall below 20 instances after removing duplicates.")





