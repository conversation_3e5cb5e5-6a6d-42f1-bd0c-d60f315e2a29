import pandas as pd

# Read the output file
df = pd.read_csv('key_questions_starting_with_did_does.csv')

print("=== SAMPLE OF KEY QUESTIONS STARTING WITH 'DID' OR 'DOES' ===")
print(f"Total records: {len(df)}")
print(f"Unique rows: {df['row_index'].nunique()}")
print(f"Unique workers: {df['worker_id'].nunique()}")

print("\n=== FIRST 10 RECORDS ===")
print(df.head(10).to_string(index=False))

print("\n=== QUESTIONS STARTING WITH 'DID' ===")
did_questions = df[df['question_text'].str.lower().str.startswith('did ')]
print(f"Found {len(did_questions)} questions starting with 'did':")
for _, row in did_questions.iterrows():
    print(f"  Row {row['row_index']}: {row['question_text']}")

print("\n=== SAMPLE QUESTIONS STARTING WITH 'DOES' ===")
does_questions = df[df['question_text'].str.lower().str.startswith('does ')]
print(f"Found {len(does_questions)} questions starting with 'does' (showing first 10):")
for _, row in does_questions.head(10).iterrows():
    print(f"  Row {row['row_index']}: {row['question_text']}")

print("\n=== BREAKDOWN BY COLUMN ===")
column_counts = df['column'].value_counts()
for col, count in column_counts.items():
    print(f"{col}: {count} questions")
