import pandas as pd
import re
import csv

print("Starting factoid source comma checker...")

# Read the CSV file
try:
    df = pd.read_csv('Tamarind600.csv')
    print(f"Successfully loaded CSV with {len(df)} rows")
except Exception as e:
    print(f"Error loading CSV: {e}")
    exit(1)

# Get factoid source columns (pattern: number. factoid_X_answer_source)
factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]

print(f"Found {len(factoid_source_columns)} factoid source columns")
print("Factoid source columns found:")
for col in sorted(factoid_source_columns):
    print(f"  - {col}")

# Initialize results list
comma_entries = []

print("\nChecking for entries with commas...")

# Process each row
for idx, row in df.iterrows():
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    task_id = row['task_id'] if 'task_id' in df.columns else 'Unknown'
    worker_id = row['worker_id']
    
    # Check factoid source columns
    for col in factoid_source_columns:
        # Skip if the column is empty
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        content = str(row[col]).strip()
        
        # Check if content contains a comma
        if ',' in content:
            # Extract round number and factoid number
            match = re.match(r'(\d+)\.\s+factoid_(\d+)_answer_source$', col)
            if match:
                round_num = match.group(1)
                factoid_num = match.group(2)
                
                comma_entries.append({
                    'row_index': idx,
                    'task_id': task_id,
                    'worker_id': worker_id,
                    'round': round_num,
                    'factoid_number': factoid_num,
                    'column': col,
                    'content': content,
                    'comma_count': content.count(',')
                })
                
                print(f"Comma found: Row {idx}, Task {task_id}, Round {round_num}, Factoid {factoid_num}")
                print(f"  Content: {content[:100]}{'...' if len(content) > 100 else ''}")
                print(f"  Comma count: {content.count(',')}")

print(f"\nProcessing complete. Found {len(comma_entries)} factoid source entries containing commas.")

# Save results to CSV
output_filename = 'factoid_sources_with_commas.csv'

with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['row_index', 'task_id', 'worker_id', 'round', 'factoid_number', 'column', 'content', 'comma_count']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    for result in comma_entries:
        writer.writerow(result)

print(f"Results saved to: {output_filename}")

if comma_entries:
    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total factoid source entries with commas: {len(comma_entries)}")
    
    # Count by round
    rounds = {}
    for entry in comma_entries:
        round_num = entry['round']
        if round_num not in rounds:
            rounds[round_num] = 0
        rounds[round_num] += 1
    
    print(f"\nEntries with commas by round:")
    for round_num in sorted(rounds.keys()):
        print(f"  Round {round_num}: {rounds[round_num]} entries")
    
    # Count by comma count
    comma_counts = {}
    for entry in comma_entries:
        count = entry['comma_count']
        if count not in comma_counts:
            comma_counts[count] = 0
        comma_counts[count] += 1
    
    print(f"\nEntries by number of commas:")
    for count in sorted(comma_counts.keys()):
        print(f"  {count} comma(s): {comma_counts[count]} entries")
    
    # Show first few examples
    print(f"\nFirst 5 examples:")
    for i, entry in enumerate(comma_entries[:5]):
        print(f"\n{i+1}. Row {entry['row_index']}, Task {entry['task_id']}, Round {entry['round']}, Factoid {entry['factoid_number']}")
        print(f"   Column: {entry['column']}")
        print(f"   Commas: {entry['comma_count']}")
        print(f"   Content: {entry['content'][:150]}{'...' if len(entry['content']) > 150 else ''}")
    
    # Show entries with most commas
    max_commas = max(entry['comma_count'] for entry in comma_entries)
    if max_commas > 1:
        print(f"\nEntries with most commas ({max_commas}):")
        high_comma_entries = [entry for entry in comma_entries if entry['comma_count'] == max_commas]
        for i, entry in enumerate(high_comma_entries[:3]):
            print(f"\n{i+1}. Row {entry['row_index']}, Task {entry['task_id']}, Round {entry['round']}, Factoid {entry['factoid_number']}")
            print(f"   Content: {entry['content'][:200]}{'...' if len(entry['content']) > 200 else ''}")

else:
    print("No factoid source entries with commas were found.")

print("\nScript completed successfully!")
