import pandas as pd
import re
from collections import defaultdict

# Read the CSV file
df = pd.read_csv('[Priority Pay +4_hr] Tamarind - Train a model to break down prompts into structured research plans _Pre-written Prompts _ [6_13].csv')

# Get column patterns
factoid_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+$', col)]
insight_columns = [col for col in df.columns if re.match(r'\d+\.\s+insight_\d+$', col)]
instruction_columns = [col for col in df.columns if re.match(r'\d+\.\s+instruction_following_\d+$', col)]

# Initialize new columns
df['rounds_with_factoids'] = 0
df['rounds_with_insights'] = 0
df['rounds_with_instruction_following'] = 0

# Process each row
for idx, row in df.iterrows():
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    # Track rounds for each content type
    factoid_rounds = set()
    insight_rounds = set()
    instruction_rounds = set()
    
    # Check factoid columns
    for factoid_col in factoid_columns:
        # Skip if the factoid column is empty
        if pd.isna(row[factoid_col]) or str(row[factoid_col]).strip() == '':
            continue
            
        # Extract round number
        round_match = re.match(r'(\d+)\.', factoid_col)
        if round_match:
            round_num = round_match.group(1)
            factoid_rounds.add(round_num)
    
    # Check insight columns
    for insight_col in insight_columns:
        # Skip if the insight column is empty
        if pd.isna(row[insight_col]) or str(row[insight_col]).strip() == '':
            continue
            
        # Extract round number
        round_match = re.match(r'(\d+)\.', insight_col)
        if round_match:
            round_num = round_match.group(1)
            insight_rounds.add(round_num)
    
    # Check instruction_following columns
    for instruction_col in instruction_columns:
        # Skip if the instruction column is empty
        if pd.isna(row[instruction_col]) or str(row[instruction_col]).strip() == '':
            continue
            
        # Extract round number
        round_match = re.match(r'(\d+)\.', instruction_col)
        if round_match:
            round_num = round_match.group(1)
            instruction_rounds.add(round_num)
    
    # Update the dataframe with round counts
    df.at[idx, 'rounds_with_factoids'] = len(factoid_rounds)
    df.at[idx, 'rounds_with_insights'] = len(insight_rounds)
    df.at[idx, 'rounds_with_instruction_following'] = len(instruction_rounds)

# Save the updated dataframe
output_filename = 'factoid_completeness_data_with_round_counts.csv'
df.to_csv(output_filename, index=False)

print(f"Updated dataframe saved to: {output_filename}")
print(f"Added columns:")
print(f"  - rounds_with_factoids: Count of distinct rounds containing factoids")
print(f"  - rounds_with_insights: Count of distinct rounds containing insights") 
print(f"  - rounds_with_instruction_following: Count of distinct rounds containing instruction following")
print(f"\nDataframe shape: {df.shape}")
print(f"Sample of new columns:")
print(df[['rounds_with_factoids', 'rounds_with_insights', 'rounds_with_instruction_following']].head(10))
