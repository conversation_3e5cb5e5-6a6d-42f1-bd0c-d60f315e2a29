import pandas as pd
import re
import csv

print("Starting combined comma-separated URL checker...")

# Read the CSV file
try:
    df = pd.read_csv('Tamarind200.csv')
    print(f"Successfully loaded CSV with {len(df)} rows")
except Exception as e:
    print(f"Error loading CSV: {e}")
    exit(1)

# Get factoid source columns (pattern: number. factoid_X_answer_source)
factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]

print(f"Found {len(factoid_source_columns)} factoid source columns")
print("Factoid source columns found:")
for col in sorted(factoid_source_columns):
    print(f"  - {col}")

# Initialize results lists
non_fragment_comma_entries = []
fragment_multi_url_entries = []

print("\nChecking for comma-separated URLs...")
print("1. Non-fragment entries with commas")
print("2. Text fragment entries with comma+space or comma+newline")

# Process each row
for idx, row in df.iterrows():
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    task_id = row['task_id'] if 'task_id' in df.columns else 'Unknown'
    worker_id = row['worker_id']
    
    # Check factoid source columns
    for col in factoid_source_columns:
        # Skip if the column is empty
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        content = str(row[col]).strip()
        
        # Extract round number and factoid number
        match = re.match(r'(\d+)\.\s+factoid_(\d+)_answer_source$', col)
        if not match:
            continue
            
        round_num = match.group(1)
        factoid_num = match.group(2)
        
        # Check for non-fragment entries with commas
        if ',' in content and '#:~:text=' not in content:
            non_fragment_comma_entries.append({
                'row_index': idx,
                'task_id': task_id,
                'worker_id': worker_id,
                'round': round_num,
                'factoid_number': factoid_num,
                'column': col,
                'content': content,
                'comma_count': content.count(','),
                'type': 'non_fragment'
            })
            
            print(f"Non-fragment comma: Row {idx}, Task {task_id}, Round {round_num}, Factoid {factoid_num}")
            print(f"  Content: {content[:100]}{'...' if len(content) > 100 else ''}")
            print(f"  Comma count: {content.count(',')}")
        
        # Check for text fragment entries with comma+space or comma+newline
        elif '#:~:text=' in content and (', ' in content or ',\n' in content):
            # Count comma-space and comma-newline patterns
            comma_space_count = content.count(', ')
            comma_newline_count = content.count(',\n')
            total_pattern_count = comma_space_count + comma_newline_count
            
            # Extract the text fragment part for analysis
            fragment_start = content.find('#:~:text=')
            fragment_part = content[fragment_start:] if fragment_start != -1 else ''
            
            fragment_multi_url_entries.append({
                'row_index': idx,
                'task_id': task_id,
                'worker_id': worker_id,
                'round': round_num,
                'factoid_number': factoid_num,
                'column': col,
                'content': content,
                'fragment_part': fragment_part,
                'comma_space_count': comma_space_count,
                'comma_newline_count': comma_newline_count,
                'total_pattern_count': total_pattern_count,
                'type': 'fragment_multi_url'
            })
            
            print(f"Fragment multi-URL: Row {idx}, Task {task_id}, Round {round_num}, Factoid {factoid_num}")
            print(f"  Content: {content[:100]}{'...' if len(content) > 100 else ''}")
            print(f"  Comma+space: {comma_space_count}, Comma+newline: {comma_newline_count}")

# Combine all results
all_comma_entries = []

# Add non-fragment entries
for entry in non_fragment_comma_entries:
    all_comma_entries.append(entry)

# Add fragment multi-URL entries
for entry in fragment_multi_url_entries:
    all_comma_entries.append(entry)

print(f"\nProcessing complete.")
print(f"Non-fragment comma entries: {len(non_fragment_comma_entries)}")
print(f"Fragment multi-URL entries: {len(fragment_multi_url_entries)}")
print(f"Total comma-separated URL entries: {len(all_comma_entries)}")

# Save combined results to CSV
output_filename = 'all_comma_separated_urls.csv'

with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
    # Use all possible fieldnames
    fieldnames = ['row_index', 'task_id', 'worker_id', 'round', 'factoid_number', 'column', 'content', 
                  'type', 'comma_count', 'fragment_part', 'comma_space_count', 'comma_newline_count', 'total_pattern_count']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    for result in all_comma_entries:
        # Fill in missing fields with empty values for consistency
        row_data = {field: result.get(field, '') for field in fieldnames}
        writer.writerow(row_data)

print(f"Combined results saved to: {output_filename}")

if all_comma_entries:
    # Print summary
    print(f"\n=== COMBINED SUMMARY ===")
    print(f"Total entries with comma-separated URLs: {len(all_comma_entries)}")
    
    # Count by type
    non_fragment_count = len([e for e in all_comma_entries if e['type'] == 'non_fragment'])
    fragment_count = len([e for e in all_comma_entries if e['type'] == 'fragment_multi_url'])
    
    print(f"\nBy type:")
    print(f"  Non-fragment comma entries: {non_fragment_count}")
    print(f"  Fragment multi-URL entries: {fragment_count}")
    
    # Count by round
    rounds = {}
    for entry in all_comma_entries:
        round_num = entry['round']
        if round_num not in rounds:
            rounds[round_num] = 0
        rounds[round_num] += 1
    
    print(f"\nEntries by round:")
    for round_num in sorted(rounds.keys()):
        print(f"  Round {round_num}: {rounds[round_num]} entries")
    
    # Show examples from each type
    print(f"\nExamples by type:")
    
    # Non-fragment examples
    non_fragment_examples = [e for e in all_comma_entries if e['type'] == 'non_fragment'][:3]
    if non_fragment_examples:
        print(f"\nNon-fragment comma entries (first 3):")
        for i, entry in enumerate(non_fragment_examples):
            print(f"  {i+1}. Row {entry['row_index']}, Task {entry['task_id']}, Round {entry['round']}, Factoid {entry['factoid_number']}")
            print(f"     Commas: {entry['comma_count']}")
            print(f"     Content: {entry['content'][:120]}{'...' if len(entry['content']) > 120 else ''}")
    
    # Fragment examples
    fragment_examples = [e for e in all_comma_entries if e['type'] == 'fragment_multi_url'][:3]
    if fragment_examples:
        print(f"\nFragment multi-URL entries (first 3):")
        for i, entry in enumerate(fragment_examples):
            print(f"  {i+1}. Row {entry['row_index']}, Task {entry['task_id']}, Round {entry['round']}, Factoid {entry['factoid_number']}")
            print(f"     Patterns: {entry['comma_space_count']} comma+space, {entry['comma_newline_count']} comma+newline")
            print(f"     Content: {entry['content'][:120]}{'...' if len(entry['content']) > 120 else ''}")

else:
    print("No comma-separated URL entries were found.")

print("\nScript completed successfully!")
