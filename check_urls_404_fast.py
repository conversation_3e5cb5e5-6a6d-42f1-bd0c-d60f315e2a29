import pandas as pd
import re
import requests
import time
from urllib.parse import urlparse
import csv
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Read the CSV file
df = pd.read_csv('Tamarind600.csv')

# Get factoid source columns (pattern: number. factoid_X_answer_source)
factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]

# Get insight source columns (pattern: number. insight_X_source)
insight_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+insight_\d+_source$', col)]

print(f"Found {len(factoid_source_columns)} factoid source columns")
print(f"Found {len(insight_source_columns)} insight source columns")

# Thread-safe results list
url_404_results = []
results_lock = threading.Lock()
checked_count = 0
count_lock = threading.Lock()

def is_valid_url(url):
    """Check if the string is a valid URL"""
    try:
        result = urlparse(str(url))
        return all([result.scheme, result.netloc])
    except:
        return False

def check_url_404(url_info):
    """Check if URL returns 404 error"""
    global checked_count
    
    url = url_info['url']
    try:
        response = requests.head(url, timeout=5, allow_redirects=True)
        is_404 = response.status_code == 404
        
        with count_lock:
            checked_count += 1
            if checked_count % 50 == 0:
                print(f"Checked {checked_count} URLs so far...")
        
        if is_404:
            with results_lock:
                url_404_results.append(url_info)
                print(f"404 found: {url_info['task_id']}, Round {url_info['round']}, {url_info['type']} {url_info['instance_within_round']}: {url}")
        
        return is_404
        
    except requests.exceptions.RequestException as e:
        with count_lock:
            checked_count += 1
            if checked_count % 50 == 0:
                print(f"Checked {checked_count} URLs so far...")
        # If there's any connection error, we'll consider it as not a 404
        return False

# Collect all URLs to check
urls_to_check = []

print("Collecting URLs...")

for idx, row in df.iterrows():
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    task_id = row['task_id'] if 'task_id' in df.columns else 'Unknown'
    
    # Check factoid source columns
    for col in factoid_source_columns:
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        url = str(row[col]).strip()
        
        if not is_valid_url(url):
            continue
            
        # Extract round number and factoid number
        match = re.match(r'(\d+)\.\s+factoid_(\d+)_answer_source$', col)
        if match:
            round_num = match.group(1)
            factoid_num = match.group(2)
            
            urls_to_check.append({
                'task_id': task_id,
                'round': round_num,
                'type': 'factoid',
                'instance_within_round': factoid_num,
                'column': col,
                'url': url
            })
    
    # Check insight source columns
    for col in insight_source_columns:
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        url = str(row[col]).strip()
        
        if not is_valid_url(url):
            continue
            
        # Extract round number and insight number
        match = re.match(r'(\d+)\.\s+insight_(\d+)_source$', col)
        if match:
            round_num = match.group(1)
            insight_num = match.group(2)
            
            urls_to_check.append({
                'task_id': task_id,
                'round': round_num,
                'type': 'insight',
                'instance_within_round': insight_num,
                'column': col,
                'url': url
            })

print(f"Found {len(urls_to_check)} URLs to check")
print("Starting URL checks with 10 concurrent threads...")

# Check URLs concurrently
start_time = time.time()

with ThreadPoolExecutor(max_workers=10) as executor:
    # Submit all URL checks
    future_to_url = {executor.submit(check_url_404, url_info): url_info for url_info in urls_to_check}
    
    # Process completed futures
    for future in as_completed(future_to_url):
        try:
            future.result()  # This will raise an exception if the function call failed
        except Exception as e:
            url_info = future_to_url[future]
            print(f"Error checking URL {url_info['url']}: {e}")

end_time = time.time()
print(f"\nProcessing complete in {end_time - start_time:.2f} seconds.")
print(f"Checked {len(urls_to_check)} total URLs. Found {len(url_404_results)} URLs returning 404 errors.")

# Save results to CSV
if url_404_results:
    output_filename = 'urls_returning_404.csv'
    
    with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['task_id', 'round', 'type', 'instance_within_round', 'column', 'url']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in url_404_results:
            writer.writerow(result)
    
    print(f"Results saved to: {output_filename}")
    
    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total 404 URLs found: {len(url_404_results)}")
    
    # Count by type
    factoid_404s = len([r for r in url_404_results if r['type'] == 'factoid'])
    insight_404s = len([r for r in url_404_results if r['type'] == 'insight'])
    
    print(f"Factoid 404s: {factoid_404s}")
    print(f"Insight 404s: {insight_404s}")
    
    # Show first few results
    print(f"\nFirst 10 404 URLs found:")
    for i, result in enumerate(url_404_results[:10]):
        print(f"  {i+1}. Task {result['task_id']}, Round {result['round']}, {result['type']} {result['instance_within_round']}: {result['url']}")

else:
    print("No URLs returning 404 errors were found.")
    # Create empty CSV file
    with open('urls_returning_404.csv', 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['task_id', 'round', 'type', 'instance_within_round', 'column', 'url']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
    print("Empty results file created: urls_returning_404.csv")