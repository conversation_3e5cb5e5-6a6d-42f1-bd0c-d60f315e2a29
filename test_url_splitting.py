import pandas as pd
import re
from urllib.parse import urlparse

def is_valid_url(url):
    """Check if the string is a valid URL"""
    try:
        result = urlparse(str(url))
        return all([result.scheme, result.netloc])
    except:
        return False

def split_urls(url_text):
    """Split URLs that may be comma-separated or newline-separated"""
    if pd.isna(url_text) or str(url_text).strip() == '':
        return []
    
    # Convert to string and split by both commas and newlines
    url_text = str(url_text).strip()
    
    # Split by commas first, then by newlines
    urls = []
    for part in url_text.split(','):
        for url in part.split('\n'):
            url = url.strip()
            if url and is_valid_url(url):
                urls.append(url)
    
    return urls

# Test the URL splitting function
test_cases = [
    "https://example.com",
    "https://example.com, https://google.com",
    "https://example.com,https://google.com",
    "https://example.com\nhttps://google.com",
    "https://example.com, https://google.com\nhttps://yahoo.com",
    "not a url",
    "",
    None
]

print("Testing URL splitting function:")
for i, test in enumerate(test_cases):
    result = split_urls(test)
    print(f"Test {i+1}: '{test}' -> {result}")

# Now test with actual data
print("\n" + "="*50)
print("Testing with actual CSV data:")

try:
    df = pd.read_csv('Tamarind600.csv')
    print(f"Successfully loaded CSV with {len(df)} rows")
    
    # Get factoid source columns
    factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]
    insight_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+insight_\d+_source$', col)]
    
    print(f"Found {len(factoid_source_columns)} factoid source columns")
    print(f"Found {len(insight_source_columns)} insight source columns")
    
    # Check first few rows for multi-URL examples
    multi_url_examples = []
    total_urls_before = 0
    total_urls_after = 0
    
    for idx, row in df.head(20).iterrows():
        if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
            continue
            
        for col in factoid_source_columns + insight_source_columns:
            if pd.isna(row[col]) or str(row[col]).strip() == '':
                continue
                
            original_text = str(row[col]).strip()
            urls = split_urls(row[col])
            
            total_urls_before += 1
            total_urls_after += len(urls)
            
            if len(urls) > 1:
                multi_url_examples.append({
                    'row': idx,
                    'column': col,
                    'original': original_text,
                    'split_urls': urls
                })
    
    print(f"\nURL splitting results from first 20 rows:")
    print(f"Original URL fields: {total_urls_before}")
    print(f"Total URLs after splitting: {total_urls_after}")
    print(f"Fields with multiple URLs: {len(multi_url_examples)}")
    
    if multi_url_examples:
        print(f"\nExamples of multi-URL fields:")
        for i, example in enumerate(multi_url_examples[:3]):
            print(f"Example {i+1}:")
            print(f"  Row {example['row']}, Column: {example['column']}")
            print(f"  Original: {example['original'][:100]}...")
            print(f"  Split into {len(example['split_urls'])} URLs:")
            for j, url in enumerate(example['split_urls']):
                print(f"    {j+1}. {url}")
    
except Exception as e:
    print(f"Error: {e}")

print("\nURL splitting test completed!")
