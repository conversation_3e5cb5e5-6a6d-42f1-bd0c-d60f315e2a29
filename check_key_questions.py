import pandas as pd
import re

# Read the CSV file
df = pd.read_csv('[Priority Pay +4_hr] Tamarind - Train a model to break down prompts into structured research plans _Pre-written Prompts _ [6_13].csv')

# Get key_question columns (pattern: number. key_question)
key_question_columns = [col for col in df.columns if re.match(r'\d+\.\s+key_question$', col)]

print(f"Found {len(key_question_columns)} key_question columns:")
for col in sorted(key_question_columns):
    print(f"  - {col}")

# Initialize results
matching_rows = []

# Process each row
for idx, row in df.iterrows():
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    # Check each key_question column
    matches_found = []
    
    for col in key_question_columns:
        # Skip if the column is empty
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        # Get the text and check if it starts with "did" or "does" (case insensitive)
        question_text = str(row[col]).strip()
        if question_text.lower().startswith('did ') or question_text.lower().startswith('does '):
            matches_found.append({
                'column': col,
                'text': question_text
            })
    
    # If any matches found, add this row to results
    if matches_found:
        matching_rows.append({
            'row_index': idx,
            'task_id': row['task_id'] if 'task_id' in df.columns else 'Unknown',
            'task_response_id': row['task_response_id'] if 'task_response_id' in df.columns else 'Unknown',
            'worker_id': row['worker_id'],
            'matches': matches_found
        })

# Print results
print(f"\n=== KEY QUESTION ANALYSIS RESULTS ===")
print(f"Total rows processed: {len(df[df['worker_id'].notna() & (df['worker_id'] != '')])}")
print(f"Rows with key questions starting with 'did' or 'does': {len(matching_rows)}")

if matching_rows:
    print(f"\n=== ROWS WITH KEY QUESTIONS STARTING WITH 'DID' OR 'DOES' ===")
    for result in matching_rows:
        print(f"\nRow {result['row_index']} (Task ID: {result['task_id']}, Response ID: {result['task_response_id']}, Worker ID: {result['worker_id']}):")
        for match in result['matches']:
            print(f"  - {match['column']}: '{match['text']}'")
else:
    print("\nNo key questions found starting with 'did' or 'does'.")

# Create a summary dataframe of matching rows
if matching_rows:
    summary_data = []
    for result in matching_rows:
        for match in result['matches']:
            summary_data.append({
                'row_index': result['row_index'],
                'task_id': result['task_id'],
                'task_response_id': result['task_response_id'],
                'worker_id': result['worker_id'],
                'column': match['column'],
                'question_text': match['text']
            })
    
    summary_df = pd.DataFrame(summary_data)
    output_filename = 'key_questions_starting_with_did_does.csv'
    summary_df.to_csv(output_filename, index=False)
    print(f"\nDetailed results saved to: {output_filename}")
    
    # Show statistics
    print(f"\n=== STATISTICS ===")
    print(f"Total matching key questions: {len(summary_data)}")
    print(f"Questions starting with 'did': {len([x for x in summary_data if x['question_text'].lower().startswith('did ')])}")
    print(f"Questions starting with 'does': {len([x for x in summary_data if x['question_text'].lower().startswith('does ')])}")
    
    print(f"\nBreakdown by column:")
    column_counts = summary_df['column'].value_counts()
    for col, count in column_counts.items():
        print(f"  - {col}: {count} questions")
