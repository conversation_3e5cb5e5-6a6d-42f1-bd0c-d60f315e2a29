import pandas as pd
import re
import csv

print("Starting text fragment multi-URL checker...")

# Read the CSV file
try:
    df = pd.read_csv('Tamarind600.csv')
    print(f"Successfully loaded CSV with {len(df)} rows")
except Exception as e:
    print(f"Error loading CSV: {e}")
    exit(1)

# Get factoid source columns (pattern: number. factoid_X_answer_source)
factoid_source_columns = [col for col in df.columns if re.match(r'\d+\.\s+factoid_\d+_answer_source$', col)]

print(f"Found {len(factoid_source_columns)} factoid source columns")
print("Factoid source columns found:")
for col in sorted(factoid_source_columns):
    print(f"  - {col}")

# Initialize results list
multi_url_fragment_entries = []

print("\nChecking for text fragment entries with comma+space or comma+newline...")

# Process each row
for idx, row in df.iterrows():
    # Skip rows without a worker_id
    if 'worker_id' not in df.columns or pd.isna(row['worker_id']) or str(row['worker_id']).strip() == '':
        continue
    
    task_id = row['task_id'] if 'task_id' in df.columns else 'Unknown'
    worker_id = row['worker_id']
    
    # Check factoid source columns
    for col in factoid_source_columns:
        # Skip if the column is empty
        if pd.isna(row[col]) or str(row[col]).strip() == '':
            continue
            
        content = str(row[col]).strip()
        
        # Check if content contains text fragment AND comma+space or comma+newline
        if '#:~:text=' in content and (', ' in content or ',\n' in content):
            # Extract round number and factoid number
            match = re.match(r'(\d+)\.\s+factoid_(\d+)_answer_source$', col)
            if match:
                round_num = match.group(1)
                factoid_num = match.group(2)
                
                # Count comma-space and comma-newline patterns
                comma_space_count = content.count(', ')
                comma_newline_count = content.count(',\n')
                total_pattern_count = comma_space_count + comma_newline_count
                
                # Extract the text fragment part for analysis
                fragment_start = content.find('#:~:text=')
                fragment_part = content[fragment_start:] if fragment_start != -1 else ''
                
                multi_url_fragment_entries.append({
                    'row_index': idx,
                    'task_id': task_id,
                    'worker_id': worker_id,
                    'round': round_num,
                    'factoid_number': factoid_num,
                    'column': col,
                    'content': content,
                    'fragment_part': fragment_part,
                    'comma_space_count': comma_space_count,
                    'comma_newline_count': comma_newline_count,
                    'total_pattern_count': total_pattern_count
                })
                
                print(f"Multi-URL fragment found: Row {idx}, Task {task_id}, Round {round_num}, Factoid {factoid_num}")
                print(f"  Content: {content[:100]}{'...' if len(content) > 100 else ''}")
                print(f"  Fragment: {fragment_part[:80]}{'...' if len(fragment_part) > 80 else ''}")
                print(f"  Comma+space: {comma_space_count}, Comma+newline: {comma_newline_count}")

print(f"\nProcessing complete. Found {len(multi_url_fragment_entries)} text fragment entries with comma+space/newline.")

# Save results to CSV
output_filename = 'text_fragment_multi_urls.csv'

with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['row_index', 'task_id', 'worker_id', 'round', 'factoid_number', 'column', 'content', 'fragment_part', 'comma_space_count', 'comma_newline_count', 'total_pattern_count']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    for result in multi_url_fragment_entries:
        writer.writerow(result)

print(f"Results saved to: {output_filename}")

if multi_url_fragment_entries:
    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Total text fragment entries with comma+space/newline: {len(multi_url_fragment_entries)}")
    
    # Count by round
    rounds = {}
    for entry in multi_url_fragment_entries:
        round_num = entry['round']
        if round_num not in rounds:
            rounds[round_num] = 0
        rounds[round_num] += 1
    
    print(f"\nEntries by round:")
    for round_num in sorted(rounds.keys()):
        print(f"  Round {round_num}: {rounds[round_num]} entries")
    
    # Count by pattern type
    comma_space_only = len([e for e in multi_url_fragment_entries if e['comma_space_count'] > 0 and e['comma_newline_count'] == 0])
    comma_newline_only = len([e for e in multi_url_fragment_entries if e['comma_space_count'] == 0 and e['comma_newline_count'] > 0])
    both_patterns = len([e for e in multi_url_fragment_entries if e['comma_space_count'] > 0 and e['comma_newline_count'] > 0])
    
    print(f"\nPattern breakdown:")
    print(f"  Comma+space only: {comma_space_only} entries")
    print(f"  Comma+newline only: {comma_newline_only} entries")
    print(f"  Both patterns: {both_patterns} entries")
    
    # Count by total pattern count
    pattern_counts = {}
    for entry in multi_url_fragment_entries:
        count = entry['total_pattern_count']
        if count not in pattern_counts:
            pattern_counts[count] = 0
        pattern_counts[count] += 1
    
    print(f"\nEntries by total comma+space/newline count:")
    for count in sorted(pattern_counts.keys()):
        print(f"  {count} pattern(s): {pattern_counts[count]} entries")
    
    # Show first few examples
    print(f"\nFirst 5 examples:")
    for i, entry in enumerate(multi_url_fragment_entries[:5]):
        print(f"\n{i+1}. Row {entry['row_index']}, Task {entry['task_id']}, Round {entry['round']}, Factoid {entry['factoid_number']}")
        print(f"   Column: {entry['column']}")
        print(f"   Patterns: {entry['comma_space_count']} comma+space, {entry['comma_newline_count']} comma+newline")
        print(f"   Content: {entry['content'][:150]}{'...' if len(entry['content']) > 150 else ''}")
        print(f"   Fragment: {entry['fragment_part'][:100]}{'...' if len(entry['fragment_part']) > 100 else ''}")
    
    # Show entries with most patterns
    max_patterns = max(entry['total_pattern_count'] for entry in multi_url_fragment_entries)
    if max_patterns > 1:
        print(f"\nEntries with most patterns ({max_patterns}):")
        high_pattern_entries = [entry for entry in multi_url_fragment_entries if entry['total_pattern_count'] == max_patterns]
        for i, entry in enumerate(high_pattern_entries[:3]):
            print(f"\n{i+1}. Row {entry['row_index']}, Task {entry['task_id']}, Round {entry['round']}, Factoid {entry['factoid_number']}")
            print(f"   Patterns: {entry['comma_space_count']} comma+space, {entry['comma_newline_count']} comma+newline")
            print(f"   Content: {entry['content'][:200]}{'...' if len(entry['content']) > 200 else ''}")

else:
    print("No text fragment entries with comma+space/newline patterns were found.")

print("\nScript completed successfully!")
